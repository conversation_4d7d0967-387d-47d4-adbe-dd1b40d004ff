    <?php
        $tablePagination = $this->roles->isNotEmpty() && $this->totalRoles > 5 ? true : false;
        $rolesAvailable = $tablePagination;
        $rowNumber = 0; // Initialize row number
    ?>
    <div>

        <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('backend.settings.system.roles.create-roles-component', []);

$__html = app('livewire')->mount($__name, $__params, 'lw-3317862618-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>

        
        <div>
            <?php if (isset($component)) { $__componentOriginal2848fab3424fc8162748b5c6984d5047 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2848fab3424fc8162748b5c6984d5047 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.filter','data' => ['filters' => [
                [
                    'name' => 'filterRolesByType',
                    'label' => 'Choose by type',
                    'options' =>$this->roleTypeOptions,
                ],
            ],'results' => $rolesAvailable]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filter'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['filters' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
                [
                    'name' => 'filterRolesByType',
                    'label' => 'Choose by type',
                    'options' =>$this->roleTypeOptions,
                ],
            ]),'results' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($rolesAvailable)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2848fab3424fc8162748b5c6984d5047)): ?>
<?php $attributes = $__attributesOriginal2848fab3424fc8162748b5c6984d5047; ?>
<?php unset($__attributesOriginal2848fab3424fc8162748b5c6984d5047); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2848fab3424fc8162748b5c6984d5047)): ?>
<?php $component = $__componentOriginal2848fab3424fc8162748b5c6984d5047; ?>
<?php unset($__componentOriginal2848fab3424fc8162748b5c6984d5047); ?>
<?php endif; ?>
        </div>

        
        <?php if (isset($component)) { $__componentOriginal9b33c063a2222f59546ad2a2a9a94bc6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9b33c063a2222f59546ad2a2a9a94bc6 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.search','data' => ['results' => $rolesAvailable,'searchProperty' => 'rolesSearch','searchPlaceholder' => 'Search by role name and description....','formWidth' => 'max-w-full my-3']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('search'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['results' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($rolesAvailable),'searchProperty' => 'rolesSearch','searchPlaceholder' => 'Search by role name and description....','formWidth' => 'max-w-full my-3']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9b33c063a2222f59546ad2a2a9a94bc6)): ?>
<?php $attributes = $__attributesOriginal9b33c063a2222f59546ad2a2a9a94bc6; ?>
<?php unset($__attributesOriginal9b33c063a2222f59546ad2a2a9a94bc6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9b33c063a2222f59546ad2a2a9a94bc6)): ?>
<?php $component = $__componentOriginal9b33c063a2222f59546ad2a2a9a94bc6; ?>
<?php unset($__componentOriginal9b33c063a2222f59546ad2a2a9a94bc6); ?>
<?php endif; ?>

        
        <?php if (isset($component)) { $__componentOriginal163c8ba6efb795223894d5ffef5034f5 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal163c8ba6efb795223894d5ffef5034f5 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.table','data' => ['columns' => [
            [
                'label' => '#',
                'headerClass' => 'border-slate-600',
            ],
            [
                'label' => 'Role name',
                'columnName' => 'name', // <-- for the arrow
                'headerClass' => 'border-l border-slate-600 w-48',
            ],
            [
                'label' => 'Role Type',
                'columnName' => 'type',
                'headerClass' => 'border-l border-slate-600 w-32 ',
            ],
            [
                'label' => 'Description',
                'headerClass' => 'border-l border-slate-600 line-clamp-1 text-center',
            ],
            [
                'label' => 'Actions',
                'headerClass' => 'border-l border-slate-600 w-24',
            ],
        ],'data' => $this->roles,'captionClass' => 'text-slate-200','sortDirection' => $sortDirection,'sortColumn' => $sortColumn,'results' => $rolesAvailable,'maxScrollHeight' => 'max-h-[500px] hidden lg:block pr-1','theadClass' => 'bg-slate-700 border-l border-slate-700','subtheadClass' => 'px-2','rowClass' => 'border-b hover:bg-slate-700 hover:text-slate-100 hover:border-y-2 hover:border-slate-600 ','tableClass' => 'border-2 border-slate-700']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('table'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['columns' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
            [
                'label' => '#',
                'headerClass' => 'border-slate-600',
            ],
            [
                'label' => 'Role name',
                'columnName' => 'name', // <-- for the arrow
                'headerClass' => 'border-l border-slate-600 w-48',
            ],
            [
                'label' => 'Role Type',
                'columnName' => 'type',
                'headerClass' => 'border-l border-slate-600 w-32 ',
            ],
            [
                'label' => 'Description',
                'headerClass' => 'border-l border-slate-600 line-clamp-1 text-center',
            ],
            [
                'label' => 'Actions',
                'headerClass' => 'border-l border-slate-600 w-24',
            ],
        ]),'data' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($this->roles),'captionClass' => 'text-slate-200','sort-direction' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($sortDirection),'sort-column' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($sortColumn),'results' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($rolesAvailable),'maxScrollHeight' => 'max-h-[500px] hidden lg:block pr-1','theadClass' => 'bg-slate-700 border-l border-slate-700','subtheadClass' => 'px-2','rowClass' => 'border-b hover:bg-slate-700 hover:text-slate-100 hover:border-y-2 hover:border-slate-600 ','tableClass' => 'border-2 border-slate-700']); ?>

            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $this->roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <tr wire:key="<?php echo e('role-' . $role->id); ?>"
                    class="border-b bg-slate-800 hover:bg-purple-700 border-slate-700 transition-colors duration-200">
                    <td class="px-2 py-1 text-slate-200 text-center">
                        <span class="bg-slate-700 text-slate-200 px-1.5 py-0.5 rounded-full text-xs font-bold">
                            <?php echo e($loop->iteration); ?>

                        </span>
                    </td>
                    <td class="border-l border-slate-600 px-2 py-1 text-slate-200 w-48">
                        <div class="font-semibold text-slate-100"><?php echo e($role->name); ?></div>
                    </td>
                    <td class="border-l border-slate-600 px-2 py-1 text-slate-200 w-32 text-center">
                        <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium
                            <?php echo e($role->type->value === 1 ? 'bg-green-600 text-green-100' :
                               ($role->type->value === 2 ? 'bg-blue-600 text-blue-100' : 'bg-gray-600 text-gray-100')); ?>">
                            <?php echo e($role->type->label()); ?>

                        </span>
                    </td>
                    <td class="border-l border-slate-600 px-2 py-1 text-slate-200 line-clamp-1">
                        <div class="text-sm text-slate-300"><?php echo e($role->description); ?></div>
                    </td>

                    <td class="border-l border-slate-600 px-2 py-1 w-24">
                        <span class="flex justify-end items-center space-x-1">
                            
                            <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('backend.settings.system.roles.view-roles-component', ['role' => $role]);

$__html = app('livewire')->mount($__name, $__params, 'view-role-' . $role->id, $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                                <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('backend.settings.system.roles.update-roles-component', ['role' => $role]);

$__html = app('livewire')->mount($__name, $__params, 'update-role-' . $role->id, $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                                    <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('backend.settings.system.roles.delete-roles-component', ['role' => $role]);

$__html = app('livewire')->mount($__name, $__params, 'delete-role-' . $role->id, $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                        </span>

                    </td>
                </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal163c8ba6efb795223894d5ffef5034f5)): ?>
<?php $attributes = $__attributesOriginal163c8ba6efb795223894d5ffef5034f5; ?>
<?php unset($__attributesOriginal163c8ba6efb795223894d5ffef5034f5); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal163c8ba6efb795223894d5ffef5034f5)): ?>
<?php $component = $__componentOriginal163c8ba6efb795223894d5ffef5034f5; ?>
<?php unset($__componentOriginal163c8ba6efb795223894d5ffef5034f5); ?>
<?php endif; ?>

        
        <?php if (isset($component)) { $__componentOriginalace4d1b2e22e971ae2a2b53aefed4e19 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalace4d1b2e22e971ae2a2b53aefed4e19 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.table-list','data' => ['data' => $this->roles,'captionMobileClass' => 'text-slate-200','maxScrollHeight' => 'max-h-[500px] lg:hidden pr-1']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('table-list'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['data' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($this->roles),'captionMobileClass' => 'text-slate-200','maxScrollHeight' => 'max-h-[500px] lg:hidden pr-1']); ?>
            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $this->roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php
                    $rowNumber++;
                ?>
                <div wire:key="<?php echo e('role-2-' . $role->id); ?>"
                    class="p-2 duration-300 ease-in-out rounded-md shadow bg-slate-500 text-slate-50 hover:bg-purple-600 hover:shadow-lg border border-slate-400 hover:border-purple-400">
                    <div class="flex justify-between items-center mb-1 pr-1">
                        <span class="text-sm text-slate-100">Name:</span>
                        <span class="text-sm text-slate-100 font-semibold"><?php echo e($role->name); ?></span>
                    </div>
                    <div class="flex justify-between items-center mb-1 pr-1">
                        <span class="text-sm text-slate-100">Type:</span>
                        <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium
                            <?php echo e($role->type->value === 1 ? 'bg-green-600 text-green-100' :
                               ($role->type->value === 2 ? 'bg-blue-600 text-blue-100' : 'bg-gray-600 text-gray-100')); ?>">
                            <?php echo e($role->type->label()); ?>

                        </span>
                    </div>
                    <div class="mb-1 block bg-slate-800 rounded px-1.5 py-1 border border-slate-600">
                        <div class="text-amber-200 text-xs font-medium">Description:</div>
                        <div class="text-sm text-slate-200 line-clamp-2"><?php echo e($role->description); ?></div>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="flex justify-end items-center space-x-1">
                            
                            <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('backend.settings.system.roles.view-roles-component', ['role' => $role]);

$__html = app('livewire')->mount($__name, $__params, 'view-2-role-' . $role->id, $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                                <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('backend.settings.system.roles.update-roles-component', ['role' => $role]);

$__html = app('livewire')->mount($__name, $__params, 'update-2-role-' . $role->id, $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                                    <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('backend.settings.system.roles.delete-roles-component', ['role' => $role]);

$__html = app('livewire')->mount($__name, $__params, 'delete-2-role-' . $role->id, $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                        </span>

                        <!--[if BLOCK]><![endif]--><?php if($rowNumber >= 1): ?>
                            <span
                                class="text-right rounded-full px-2 bg-slate-950 text-slate-50 text-xs font-bold"><?php echo e($rowNumber); ?></span>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalace4d1b2e22e971ae2a2b53aefed4e19)): ?>
<?php $attributes = $__attributesOriginalace4d1b2e22e971ae2a2b53aefed4e19; ?>
<?php unset($__attributesOriginalace4d1b2e22e971ae2a2b53aefed4e19); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalace4d1b2e22e971ae2a2b53aefed4e19)): ?>
<?php $component = $__componentOriginalace4d1b2e22e971ae2a2b53aefed4e19; ?>
<?php unset($__componentOriginalace4d1b2e22e971ae2a2b53aefed4e19); ?>
<?php endif; ?>

        
        <?php if (isset($component)) { $__componentOriginal9a81ef5e6386ad2695ad8705a47608cf = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9a81ef5e6386ad2695ad8705a47608cf = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.paginator','data' => ['pages' => $this->rolesPages,'totalRecords' => $this->totalRoles,'selectClass' => 'text-slate-500','paginateRecords' => 'rolesPaginate','results' => $rolesAvailable,'paginationEnabled' => $tablePagination]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('paginator'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['pages' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($this->rolesPages),'totalRecords' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($this->totalRoles),'selectClass' => 'text-slate-500','paginateRecords' => 'rolesPaginate','results' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($rolesAvailable),'paginationEnabled' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($tablePagination)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9a81ef5e6386ad2695ad8705a47608cf)): ?>
<?php $attributes = $__attributesOriginal9a81ef5e6386ad2695ad8705a47608cf; ?>
<?php unset($__attributesOriginal9a81ef5e6386ad2695ad8705a47608cf); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9a81ef5e6386ad2695ad8705a47608cf)): ?>
<?php $component = $__componentOriginal9a81ef5e6386ad2695ad8705a47608cf; ?>
<?php unset($__componentOriginal9a81ef5e6386ad2695ad8705a47608cf); ?>
<?php endif; ?>

    </div>
<?php /**PATH C:\Users\<USER>\Herd\laraauthstarter\resources\views/livewire/backend/settings/system/roles/show-roles-component.blade.php ENDPATH**/ ?>