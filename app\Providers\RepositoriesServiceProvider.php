<?php

namespace App\Providers;

use App\Supports\CacheToggle;
use Illuminate\Support\ServiceProvider;
use App\Repositories\Roles\GetRolesRepository;
use App\Repositories\Roles\CountRolesRepository;
use App\Repositories\Roles\GetRoleByIdRepository;
use App\Repositories\Roles\CachedGetRolesRepository;
use App\Repositories\Roles\CachedCountRolesRepository;
use App\Repositories\Roles\CachedGetRoleByIdRepository;
use App\Repositories\BlockedIps\GetBlockedIpsRepository;
use App\Repositories\BlockedIps\CountBlockedIpsRepository;
use App\Repositories\BlockedIps\GetBlockedIpByIdRepository;
use App\Repositories\BlockedIps\CachedGetBlockedIpsRepository;
use App\Contracts\Repos\RolesRepos\GetRolesRepositoryInterface;
use App\Repositories\BlockedIps\CachedCountBlockedIpsRepository;
use App\Contracts\Repos\RolesRepos\CountRolesRepositoryInterface;
use App\Repositories\BlockedIps\CachedGetBlockedIpByIdRepository;
use App\Contracts\Repos\RolesRepos\GetRoleByIdRepositoryInterface;
use App\Repositories\UntrustedHeaders\GetUntrustedHeadersRepository;
use App\Repositories\UntrustedHeaders\CountUntrustedHeadersRepository;
use App\Repositories\UntrustedHeaders\GetUntrustedHeaderByIdRepository;
use App\Repositories\AllowedExternalIps\GetAllowedExternalIpsRepository;
use App\Contracts\Repos\BlockedIpsRepos\GetBlockedIpsRepositoryInterface;
use App\Repositories\AllowedExternalIps\CountAllowedExternalIpsRepository;
use App\Repositories\UntrustedHeaders\CachedGetUntrustedHeadersRepository;
use App\Contracts\Repos\BlockedIpsRepos\CountBlockedIpsRepositoryInterface;
use App\Repositories\AllowedExternalIps\GetAllowedExternalIpByIdRepository;
use App\Contracts\Repos\BlockedIpsRepos\GetBlockedIpByIdRepositoryInterface;
use App\Repositories\UntrustedHeaders\CachedCountUntrustedHeadersRepository;
use App\Repositories\UntrustedHeaders\CachedGetUntrustedHeaderByIdRepository;
use App\Repositories\AllowedExternalIps\CachedGetAllowedExternalIpsRepository;
use App\Repositories\AllowedMails\GetAllowedMailByIdRepository;
use App\Contracts\Repos\AllowedMailsRepos\GetAllowedMailByIdRepositoryInterface;
use App\Repositories\AllowedMails\GetAllowedMailsRepository;
use App\Repositories\AllowedMails\CountAllowedMailsRepository;
use App\Repositories\AllowedMails\CachedGetAllowedMailsRepository;
use App\Repositories\AllowedMails\CachedCountAllowedMailsRepository;
use App\Repositories\AllowedMails\CachedGetAllowedMailByIdRepository;
use App\Repositories\AllowedExternalIps\CachedCountAllowedExternalIpsRepository;
use App\Repositories\AllowedExternalIps\CachedGetAllowedExternalIpByIdRepository;
use App\Repositories\FeatureSettings\CountFeatureSettingsRepository;
use App\Repositories\FeatureSettings\CachedCountFeatureSettingsRepository;
use App\Repositories\FeatureSettings\GetFeatureSettingsRepository;
use App\Repositories\FeatureSettings\GetFeatureSettingByIdRepository;
use App\Repositories\FeatureSettings\CachedGetFeatureSettingsRepository;
use App\Repositories\FeatureSettings\CachedGetFeatureSettingByIdRepository;
use App\Contracts\Repos\UntrustedHeadersRepos\GetUntrustedHeadersRepositoryInterface;
use App\Contracts\Repos\UntrustedHeadersRepos\CountUntrustedHeadersRepositoryInterface;
use App\Contracts\Repos\UntrustedHeadersRepos\GetUntrustedHeaderByIdRepositoryInterface;
use App\Contracts\Repos\AllowedExternalIpsRepos\GetAllowedExternalIpsRepositoryInterface;
use App\Contracts\Repos\AllowedExternalIpsRepos\CountAllowedExternalIpsRepositoryInterface;
use App\Contracts\Repos\AllowedExternalIpsRepos\GetAllowedExternalIpByIdRepositoryInterface;
use App\Contracts\Repos\FeatureSettingsRepos\CountFeatureSettingsRepositoryInterface;
use App\Contracts\Repos\FeatureSettingsRepos\GetFeatureSettingsRepositoryInterface;
use App\Contracts\Repos\FeatureSettingsRepos\GetFeatureSettingByIdRepositoryInterface;
use App\Contracts\Repos\AllowedMailsRepos\GetAllowedMailsRepositoryInterface;
use App\Contracts\Repos\AllowedMailsRepos\CountAllowedMailsRepositoryInterface;
use App\Repositories\Permissions\CountPermissionsRepository;
use App\Repositories\Permissions\CachedCountPermissionsRepository;
use App\Contracts\Repos\PermissionsRepos\CountPermissionsRepositoryInterface;



class RepositoriesServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(GetRoleByIdRepositoryInterface::class, function ($app) {
            $getRoleByIdRepository = $app->make(GetRoleByIdRepository::class);
            return CacheToggle::for('roles') ? new CachedGetRoleByIdRepository($getRoleByIdRepository) : $getRoleByIdRepository;
        });

        $this->app->singleton(CountRolesRepositoryInterface::class, function ($app) {
            $countRolesRepository = $app->make(CountRolesRepository::class);
            return CacheToggle::for('roles') ? new CachedCountRolesRepository($countRolesRepository) : $countRolesRepository;
        });

        $this->app->singleton(GetRolesRepositoryInterface::class, function ($app) {
            $getRolesRepository = $app->make(GetRolesRepository::class);
            return CacheToggle::for('roles') ? new CachedGetRolesRepository($getRolesRepository) : $getRolesRepository;
        });



        $this->app->singleton(GetUntrustedHeaderByIdRepositoryInterface::class, function ($app) {
            $getUntrustedHeadersByIdRepository = $app->make(GetUntrustedHeaderByIdRepository::class);
            return CacheToggle::for('untrusted_headers') ? new CachedGetUntrustedHeaderByIdRepository($getUntrustedHeadersByIdRepository) : $getUntrustedHeadersByIdRepository;
        });

        $this->app->singleton(CountUntrustedHeadersRepositoryInterface::class, function ($app) {
            $countUntrustedHeadersRepository = $app->make(CountUntrustedHeadersRepository::class);
            return CacheToggle::for('untrusted_headers') ? new CachedCountUntrustedHeadersRepository($countUntrustedHeadersRepository) : $countUntrustedHeadersRepository;
        });

        $this->app->singleton(GetUntrustedHeadersRepositoryInterface::class, function ($app) {
            $getUntrustedHeadersRepository = $app->make(GetUntrustedHeadersRepository::class);
            return CacheToggle::for('untrusted_headers') ? new CachedGetUntrustedHeadersRepository($getUntrustedHeadersRepository) : $getUntrustedHeadersRepository;
        });



        $this->app->singleton(GetBlockedIpByIdRepositoryInterface::class, function ($app) {
            $getBlockedIpByIdRepository = $app->make(GetBlockedIpByIdRepository::class);
            return CacheToggle::for('blocked_ips') ? new CachedGetBlockedIpByIdRepository($getBlockedIpByIdRepository) : $getBlockedIpByIdRepository;
        });

        $this->app->singleton(CountBlockedIpsRepositoryInterface::class, function ($app) {
            $countBlockedIpsRepository = $app->make(CountBlockedIpsRepository::class);
            return CacheToggle::for('blocked_ips') ? new CachedCountBlockedIpsRepository($countBlockedIpsRepository) : $countBlockedIpsRepository;
        });

        $this->app->singleton(GetBlockedIpsRepositoryInterface::class, function ($app) {
            $getBlockedIpsRepository = $app->make(GetBlockedIpsRepository::class);
            return CacheToggle::for('blocked_ips') ? new CachedGetBlockedIpsRepository($getBlockedIpsRepository) : $getBlockedIpsRepository;
        });

        $this->app->singleton(GetAllowedExternalIpByIdRepositoryInterface::class, function ($app) {
            $getAllowedExternalIpByIdRepository = $app->make(GetAllowedExternalIpByIdRepository::class);
            return CacheToggle::for('allowed_ips') ? new CachedGetAllowedExternalIpByIdRepository($getAllowedExternalIpByIdRepository) : $getAllowedExternalIpByIdRepository;
        });

        $this->app->singleton(CountAllowedExternalIpsRepositoryInterface::class, function ($app) {
            $countAllowedExternalIpsRepository = $app->make(CountAllowedExternalIpsRepository::class);
            return CacheToggle::for('allowed_ips') ? new CachedCountAllowedExternalIpsRepository($countAllowedExternalIpsRepository) : $countAllowedExternalIpsRepository;
        });

        $this->app->singleton(GetAllowedExternalIpsRepositoryInterface::class, function ($app) {
            $getAllowedExternalIpsRepository = $app->make(GetAllowedExternalIpsRepository::class);
            return CacheToggle::for('allowed_ips') ? new CachedGetAllowedExternalIpsRepository($getAllowedExternalIpsRepository) : $getAllowedExternalIpsRepository;
        });


        $this->app->singleton(GetAllowedMailByIdRepositoryInterface::class, function ($app) {
            $getAllowedMailByIdRepository = $app->make(GetAllowedMailByIdRepository::class);
            return CacheToggle::for('allowed_mails') ? new CachedGetAllowedMailByIdRepository($getAllowedMailByIdRepository) : $getAllowedMailByIdRepository;
        });

        $this->app->singleton(CountAllowedMailsRepositoryInterface::class, function ($app) {
            $countAllowedMailsRepository = $app->make(CountAllowedMailsRepository::class);
            return CacheToggle::for('allowed_mails') ? new CachedCountAllowedMailsRepository($countAllowedMailsRepository) : $countAllowedMailsRepository;
        });

        $this->app->singleton(GetAllowedMailsRepositoryInterface::class, function ($app) {
            $getAllowedMailsRepository = $app->make(GetAllowedMailsRepository::class);
            return CacheToggle::for('allowed_mails') ? new CachedGetAllowedMailsRepository($getAllowedMailsRepository) : $getAllowedMailsRepository;
        });

        $this->app->singleton(CountFeatureSettingsRepositoryInterface::class, function ($app) {
            $countFeatureSettingsRepository = $app->make(CountFeatureSettingsRepository::class);
            return CacheToggle::for('feature_settings') ? new CachedCountFeatureSettingsRepository($countFeatureSettingsRepository) : $countFeatureSettingsRepository;
        });

        $this->app->singleton(GetFeatureSettingByIdRepositoryInterface::class, function ($app) {
            $getFeatureSettingByIdRepository = $app->make(GetFeatureSettingByIdRepository::class);
            return CacheToggle::for('feature_settings') ? new CachedGetFeatureSettingByIdRepository($getFeatureSettingByIdRepository) : $getFeatureSettingByIdRepository;
        });

        $this->app->singleton(GetFeatureSettingsRepositoryInterface::class, function ($app) {
            $getFeatureSettingsRepository = $app->make(GetFeatureSettingsRepository::class);
            return CacheToggle::for('feature_settings') ? new CachedGetFeatureSettingsRepository($getFeatureSettingsRepository) : $getFeatureSettingsRepository;
        });

        $this->app->singleton(CountPermissionsRepositoryInterface::class, function ($app) {
            $countPermissionsRepository = $app->make(CountPermissionsRepository::class);
            return CacheToggle::for('permissions') ? new CachedCountPermissionsRepository($countPermissionsRepository) : $countPermissionsRepository;
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
