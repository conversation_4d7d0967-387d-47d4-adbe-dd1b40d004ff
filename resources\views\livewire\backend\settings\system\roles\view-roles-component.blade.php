<div>
    <x-action-buttons :id="$role->id" viewAction="openModalToViewRole" />

    <x-dialog-modal wire:model="revealViewRoleModal" :maxWidth="'2xl'">
        <x-slot name="content">
            <x-divider nameClass="text-purple-400">
                {{ __('ROLE DETAILS') }}
            </x-divider>

            {{-- Role Header --}}
            <div class="bg-gradient-to-r from-purple-600 to-purple-700 rounded-lg p-4 mb-4 border border-purple-500">
                <div class="flex justify-between items-center">
                    <div>
                        <h3 class="text-xl font-bold text-white">{{ $role->name }}</h3>
                        <p class="text-purple-100 text-sm">Role Information</p>
                    </div>
                    <div class="text-right">
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                            {{ $role->type->value === 1 ? 'bg-green-600 text-green-100' :
                               ($role->type->value === 2 ? 'bg-blue-600 text-blue-100' : 'bg-gray-600 text-gray-100') }}">
                            {{ $role->type->label() }}
                        </span>
                    </div>
                </div>
            </div>

            {{-- Role Details Grid --}}
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                {{-- Role Name --}}
                <div class="bg-slate-700 rounded-lg p-3 border border-slate-600">
                    <div class="text-slate-400 text-xs uppercase tracking-wide font-semibold mb-1">Role Name</div>
                    <div class="text-slate-200 text-lg font-semibold">{{ $role->name }}</div>
                </div>

                {{-- Role Slug --}}
                <div class="bg-slate-700 rounded-lg p-3 border border-slate-600">
                    <div class="text-slate-400 text-xs uppercase tracking-wide font-semibold mb-1">Slug</div>
                    <code class="text-amber-300 bg-slate-900 px-2 py-1 rounded text-sm">{{ $role->slug }}</code>
                </div>

                {{-- Role Type --}}
                <div class="bg-slate-700 rounded-lg p-3 border border-slate-600">
                    <div class="text-slate-400 text-xs uppercase tracking-wide font-semibold mb-1">Type</div>
                    <span class="inline-flex items-center px-2.5 py-1 rounded-full text-sm font-medium
                        {{ $role->type->value === 1 ? 'bg-green-600 text-green-100' :
                           ($role->type->value === 2 ? 'bg-blue-600 text-blue-100' : 'bg-gray-600 text-gray-100') }}">
                        {{ $role->type->label() }}
                    </span>
                </div>

                {{-- Created Date --}}
                {{-- <div class="bg-slate-700 rounded-lg p-3 border border-slate-600">
                    <div class="text-slate-400 text-xs uppercase tracking-wide font-semibold mb-1">Created</div>
                    <div class="text-slate-200 text-sm">{{ $role->created_at->format('M d, Y') }}</div>
                </div> --}}
            </div>

            {{-- Description --}}
            <div class="bg-slate-700 rounded-lg p-4 border border-slate-600 mb-4">
                <div class="text-slate-400 text-xs uppercase tracking-wide font-semibold mb-2">Description</div>
                <div class="text-slate-200 text-sm leading-relaxed">
                    {{ $role->description ?: 'No description provided for this role.' }}
                </div>
            </div>

            {{-- Actions --}}
            <div class="flex justify-end space-x-2 mt-6">
                <x-button class="bg-red-600 hover:bg-red-500" icon="times" wire:click="$set('revealViewRoleModal', false)">
                    {{ __('Close') }}
                </x-button>
            </div>
        </x-slot>
    </x-dialog-modal>
</div>
