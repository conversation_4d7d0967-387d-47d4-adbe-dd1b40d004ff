<div class="mt-12 py-6 px-1 mx-auto max-w-7xl">
    <div class="overflow-hidden shadow-xl sm:rounded-none">
        <div id="accordion-collapse" data-accordion="collapse"
            data-active-classes="bg-slate-600 dark:bg-gray-800 text-slate-300 dark:text-white">

            <x-accordion :id="1" title="System roles" icon="user-astronaut"
                buttonClass="rounded-t-lg bg-slate-900" expanded="true">
                @livewire('Backend.Settings.System.Roles.ShowRolesComponent', [
                    'lazy' => true,
                ])
            </x-accordion>

            <x-accordion :id="2" title="Untrusted Headers | Bots" icon="robot" buttonClass="bg-slate-900"
                expanded="false">
                @livewire('Backend.Settings.System.Headers.ShowUntrustedHeadersComponent', [
                    'lazy' => true,
                ])
            </x-accordion>

            <x-accordion :id="3" title="Blocked IP Addresses" icon="globe" expanded="false"
                buttonClass="bg-slate-900">
                @livewire('Backend.Settings.System.BlockedIps.ShowBlockedIpsComponent', [
                    'lazy' => true,
                ])
            </x-accordion>

            <x-accordion :id="4" title="Allowed External IP Addresses" icon="globe" expanded="false"
                buttonClass="bg-slate-900">
                @livewire('Backend.Settings.System.AllowedExternalIps.ShowAllowedExternalIpsComponent', [
                    'lazy' => true,
                ])
            </x-accordion>

            <x-accordion :id="5" title="Allowed Access Emails" icon="envelope" expanded="false"
                buttonClass="bg-slate-900">
                @livewire('Backend.Settings.System.AllowedMails.ShowAllowedMailsComponent', [
                    'lazy' => true,
                ])
            </x-accordion>

            <x-accordion :id="6" title="System Features settings" icon="gear" expanded="false" buttonClass="bg-slate-900">
                @livewire('Backend.Settings.System.FeatureSettings.ShowFeatureSettingsComponent', [
                    'lazy' => true
                ])
            </x-accordion>

            <x-accordion :id="7" title="System Permissions" icon="key" expanded="true" buttonClass="bg-slate-900">
                @livewire('Backend.Settings.System.Permissions.ShowPermissionsComponent', [
                    'lazy' => true,
                ])
            </x-accordion>

            <x-accordion :id="8" title="System Authorizations" icon="shield" expanded="false" buttonClass="bg-slate-900">
                @livewire('Backend.Settings.System.PermissionsRoles.ShowPermissionsRolesComponent', [
                    'lazy' => true,
                ])
            </x-accordion>



            {{-- @can('viewAny', $sheildMaster) --}}
            <x-accordion :id="9" title="System Access" expanded="false" buttonClass="bg-slate-900">
                {{-- @livewire('Backend.Sheilds.SystemSettings', [
                        'lazy' => true,
                    ]) --}}
            </x-accordion>
            {{-- @endcan --}}

            <x-accordion :id="10" title="Team members's Duties" expanded="false" buttonClass="bg-slate-900">
                {{-- @livewire('Backend.Sheilds.SystemDutiesComponent', [
                    'lazy' => true,
                ]) --}}
            </x-accordion>

        </div>
    </div>
</div>
