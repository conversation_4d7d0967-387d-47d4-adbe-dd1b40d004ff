<?php

namespace App\Repositories\Permissions;

use Illuminate\Support\Facades\Cache;
use App\Contracts\Repos\PermissionsRepos\CountPermissionsRepositoryInterface;

final class CachedCountPermissionsRepository implements CountPermissionsRepositoryInterface
{
    public function __construct(private CountPermissionsRepositoryInterface $countPermissionsRepository) {}

    public function handle(): int
    {
        $key   = 'permissions:count';
        $fresh = 300; // 5 minutes
        $stale = 900; // 15 minutes

        return Cache::flexible($key, [$fresh, $stale], function () {
            return $this->countPermissionsRepository->handle();
        });
    }
}
