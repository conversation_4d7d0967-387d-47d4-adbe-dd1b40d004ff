    @php
        $tablePagination = $this->roles->isNotEmpty() && $this->totalRoles > 5 ? true : false;
        $rolesAvailable = $tablePagination;
        $rowNumber = 0; // Initialize row number
    @endphp

    <style>
        .text-center .flex {
            justify-content: center !important;
        }
    </style>

    <div>

        <livewire:backend.settings.system.roles.create-roles-component />

        {{-- Filters and search --}}
        <div>
            <x-filter :filters="[
                [
                    'name' => 'filterRolesByType',
                    'label' => 'Choose by type',
                    'options' =>$this->roleTypeOptions,
                ],
            ]" :results="$rolesAvailable" />
        </div>

        {{-- Search --}}
        <x-search :results="$rolesAvailable" searchProperty="rolesSearch"
            searchPlaceholder="Search by role name and description...." formWidth="max-w-full my-3" />

        {{-- Table --}}
        <x-table :columns="[
            [
                'label' => '#',
                'headerClass' => 'border-slate-600',
            ],
            [
                'label' => 'Role name',
                'columnName' => 'name', // <-- for the arrow
                'headerClass' => 'border-l border-slate-600 w-48',
            ],
            [
                'label' => 'Role Type',
                'columnName' => 'type',
                'headerClass' => 'border-l border-slate-600 w-32 text-center',
            ],
            [
                'label' => 'Description',
                'headerClass' => 'border-l border-slate-600 line-clamp-1',
            ],
            [
                'label' => 'Actions',
                'headerClass' => 'border-l border-slate-600 w-24',
            ],
        ]" :data="$this->roles" captionClass="text-slate-200" :sort-direction="$sortDirection" :sort-column="$sortColumn"
            :results="$rolesAvailable" maxScrollHeight="max-h-[500px] hidden lg:block pr-1"
            theadClass="bg-slate-700 border-l border-slate-700" subtheadClass="px-2"
            rowClass="border-b hover:bg-slate-700 hover:text-slate-100 hover:border-y-2 hover:border-slate-600 "
            tableClass="border-2 border-slate-700">

            @foreach ($this->roles as $role)
                <tr wire:key="{{ 'role-' . $role->id }}"
                    class="border-b bg-slate-800 hover:bg-purple-700 border-slate-700 transition-colors duration-200">
                    <td class="px-2 py-1 text-slate-200 text-center">
                        <span class="bg-slate-700 text-slate-200 px-1.5 py-0.5 rounded-full text-xs font-bold">
                            {{ $loop->iteration }}
                        </span>
                    </td>
                    <td class="border-l border-slate-600 px-2 py-1 text-slate-200 w-48">
                        <div class="font-semibold text-slate-100">{{ $role->name }}</div>
                    </td>
                    <td class="border-l border-slate-600 px-2 py-1 text-slate-200 w-32 text-center">
                        <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium
                            {{ $role->type->value === 1 ? 'bg-green-600 text-green-100' :
                               ($role->type->value === 2 ? 'bg-blue-600 text-blue-100' : 'bg-gray-600 text-gray-100') }}">
                            {{ $role->type->label() }}
                        </span>
                    </td>
                    <td class="border-l border-slate-600 px-2 py-1 text-slate-200 line-clamp-1">
                        <div class="text-sm text-slate-300">{{ $role->description }}</div>
                    </td>

                    <td class="border-l border-slate-600 px-2 py-1 w-24">
                        <span class="flex justify-end items-center space-x-1">
                            {{-- Action buttons --}}
                            <livewire:backend.settings.system.roles.view-roles-component :$role :key="'view-role-' . $role->id">
                                <livewire:backend.settings.system.roles.update-roles-component :$role :key="'update-role-' . $role->id">
                                    <livewire:backend.settings.system.roles.delete-roles-component :$role
                                        :key="'delete-role-' . $role->id">
                        </span>

                    </td>
                </tr>
            @endforeach
        </x-table>

        {{-- Table List --}}
        <x-table-list :data="$this->roles" captionMobileClass="text-slate-200"
            maxScrollHeight="max-h-[500px] lg:hidden pr-1">
            @foreach ($this->roles as $role)
                @php
                    $rowNumber++;
                @endphp
                <div wire:key="{{ 'role-2-' . $role->id }}"
                    class="p-2 duration-300 ease-in-out rounded-md shadow bg-slate-500 text-slate-50 hover:bg-purple-600 hover:shadow-lg border border-slate-400 hover:border-purple-400">
                    <div class="flex justify-between items-center mb-1 pr-1">
                        <span class="text-sm text-slate-100">Name:</span>
                        <span class="text-sm text-slate-100 font-semibold">{{ $role->name }}</span>
                    </div>
                    <div class="flex justify-between items-center mb-1 pr-1">
                        <span class="text-sm text-slate-100">Type:</span>
                        <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium
                            {{ $role->type->value === 1 ? 'bg-green-600 text-green-100' :
                               ($role->type->value === 2 ? 'bg-blue-600 text-blue-100' : 'bg-gray-600 text-gray-100') }}">
                            {{ $role->type->label() }}
                        </span>
                    </div>
                    <div class="mb-1 block bg-slate-800 rounded px-1.5 py-1 border border-slate-600">
                        <div class="text-amber-200 text-xs font-medium">Description:</div>
                        <div class="text-sm text-slate-200 line-clamp-2">{{ $role->description }}</div>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="flex justify-end items-center space-x-1">
                            {{-- Action buttons --}}
                            <livewire:backend.settings.system.roles.view-roles-component :$role :key="'view-2-role-' . $role->id">
                                <livewire:backend.settings.system.roles.update-roles-component :$role :key="'update-2-role-' . $role->id">
                                    <livewire:backend.settings.system.roles.delete-roles-component :$role
                                        :key="'delete-2-role-' . $role->id">
                        </span>

                        @if ($rowNumber >= 1)
                            <span
                                class="text-right rounded-full px-2 bg-slate-950 text-slate-50 text-xs font-bold">{{ $rowNumber }}</span>
                        @endif
                    </div>
                </div>
            @endforeach
        </x-table-list>

        {{-- Pagination --}}
        <x-paginator :pages="$this->rolesPages" :totalRecords="$this->totalRoles" selectClass="text-slate-500" paginateRecords="rolesPaginate"
            :results="$rolesAvailable" :paginationEnabled="$tablePagination" />

    </div>
