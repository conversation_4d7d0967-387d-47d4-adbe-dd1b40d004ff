<?php

namespace App\Builders;

use Illuminate\Database\Eloquent\Builder;

class PermissionBuilder extends Builder
{
    public function orderByIdAsc(): self
    {
        return $this->orderBy('id', 'asc');
    }

    public function orderByIdDesc(): self
    {
        return $this->orderBy('id', 'desc');
    }

    public function orderByNameAsc(): self
    {
        return $this->orderBy('name', 'asc');
    }

    public function orderByNameDesc(): self
    {
        return $this->orderBy('name', 'desc');
    }

    public function orderBySlugAsc(): self
    {
        return $this->orderBy('slug', 'asc');
    }

    public function orderBySlugDesc(): self
    {
        return $this->orderBy('slug', 'desc');
    }

    public function orderByClusterAsc(): self
    {
        return $this->orderBy('cluster', 'asc');
    }

    public function orderByClusterDesc(): self
    {
        return $this->orderBy('cluster', 'desc');
    }

    public function whereCluster(int $cluster): self
    {
        return $this->where('cluster', $cluster);
    }

    public function whereSlug(string $slug): self
    {
        return $this->where('slug', $slug);
    }

    public function firstPermissionPerCluster(): self
    {
        return $this->whereIn('id', function ($query) {
                $query->selectRaw('MIN(id)')
                    ->from('permissions')
                    ->groupBy('cluster');
            });
    }
}
