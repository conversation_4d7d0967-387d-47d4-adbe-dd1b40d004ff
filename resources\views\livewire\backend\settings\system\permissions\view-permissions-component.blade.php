<div>
    <x-action-buttons :id="$permission->id" viewAction="openModalToViewPermission" />

    <x-dialog-modal wire:model="revealViewPermissionModal" :maxWidth="'2xl'">
        <x-slot name="content">
            <x-divider nameClass="text-blue-400">
                {{ __('CLUSTER PERMISSIONS') }}
            </x-divider>

            <div class="text-slate-200 text-base font-semibold my-2">
                {{ $this->clusterInfo['cluster_name'] }} ({{ $this->clusterInfo['total_permissions'] }} permissions)
            </div>

            <div class="text-slate-200 text-sm my-2">
                Showing all permissions in cluster {{ $this->clusterInfo['cluster_id'] }}:
            </div>

            {{-- Permissions Table --}}
            <div class="max-h-[400px] overflow-y-auto custom-scrollbar border border-slate-600 rounded">
                <table class="w-full text-sm text-left text-slate-400">
                    <thead class="text-xs uppercase bg-slate-700 text-slate-300">
                        <tr>
                            <th class="px-3 py-2 border-b border-slate-600">#</th>
                            <th class="px-3 py-2 border-b border-slate-600">Name</th>
                            <th class="px-3 py-2 border-b border-slate-600">Slug</th>
                            <th class="px-3 py-2 border-b border-slate-600">Description</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($this->clusterPermissions as $clusterPermission)
                            <tr class="border-b bg-slate-800 hover:bg-slate-700 border-slate-700
                                {{ $clusterPermission->id === $permission->id ? 'bg-blue-900 hover:bg-blue-800' : '' }}">
                                <td class="px-3 py-2 text-slate-200">
                                    {{ $loop->iteration }}
                                    @if($clusterPermission->id === $permission->id)
                                        <span class="ml-1 text-xs bg-blue-600 text-white px-1 py-0.5 rounded">Current</span>
                                    @endif
                                </td>
                                <td class="px-3 py-2 text-slate-200">
                                    {{ $clusterPermission->name }}
                                </td>
                                <td class="px-3 py-2 text-slate-200">
                                    <code class="text-xs bg-slate-900 px-1 py-0.5 rounded">{{ $clusterPermission->slug }}</code>
                                </td>
                                <td class="px-3 py-2 text-slate-200 line-clamp-2">
                                    {{ $clusterPermission->description }}
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <div class="flex justify-end mt-4">
                <x-button class="bg-slate-600 hover:bg-slate-500" wire:click="$set('revealViewPermissionModal', false)">
                    {{ __('Close') }}
                </x-button>
            </div>
        </x-slot>
    </x-dialog-modal>
</div>
