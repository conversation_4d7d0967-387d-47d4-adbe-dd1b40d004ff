<div>
    <x-action-buttons :id="$permission->id" viewAction="openModalToViewPermission" />

    <x-dialog-modal wire:model="revealViewPermissionModal" :maxWidth="'2xl'">
        <x-slot name="content">
            <x-divider nameClass="text-blue-400">
                {{ __('CLUSTER PERMISSIONS') }}
            </x-divider>

            <div class="text-slate-200 text-base font-semibold my-2">
                {{ $this->clusterInfo['cluster_name'] }} ({{ $this->clusterInfo['total_permissions'] }} permissions)
            </div>

            <div class="text-slate-200 text-sm my-2">
                Showing all permissions in cluster {{ $this->clusterInfo['cluster_id'] }}:
            </div>

            {{-- Permissions List --}}
            <div class="max-h-[400px] overflow-y-auto custom-scrollbar space-y-1">
                @foreach ($this->clusterPermissions as $clusterPermission)
                    <div class="p-2 duration-300 ease-in-out rounded-md shadow
                        {{ $clusterPermission->id === $permission->id
                            ? 'bg-blue-600 text-blue-50 hover:bg-blue-700 border-2 border-blue-400'
                            : 'bg-slate-500 text-slate-50 hover:bg-slate-600 hover:shadow-lg' }}">

                        {{-- Header with name and current badge --}}
                        <div class="flex justify-between items-center mb-1">
                            <div class="flex items-center space-x-2">
                                <span class="text-sm font-semibold">{{ $clusterPermission->name }}</span>
                                @if($clusterPermission->id === $permission->id)
                                    <span class="text-xs bg-blue-800 text-blue-100 px-1.5 py-0.5 rounded-full font-medium">
                                        Current
                                    </span>
                                @endif
                            </div>
                            <span class="text-right rounded-full px-1.5 py-0.5
                                {{ $clusterPermission->id === $permission->id ? 'bg-blue-800 text-blue-100' : 'bg-slate-950 text-slate-50' }}
                                text-xs">
                                #{{ $loop->iteration }}
                            </span>
                        </div>

                        {{-- Slug --}}
                        <div class="flex justify-between items-center mb-1">
                            <span class="text-sm {{ $clusterPermission->id === $permission->id ? 'text-blue-100' : 'text-slate-100' }}">
                                Slug:
                            </span>
                            <code class="text-xs px-1.5 py-0.5 rounded
                                {{ $clusterPermission->id === $permission->id ? 'bg-blue-800 text-blue-100' : 'bg-slate-900 text-slate-200' }}">
                                {{ $clusterPermission->slug }}
                            </code>
                        </div>

                        {{-- Description --}}
                        <div class="block rounded px-1.5 py-1
                            {{ $clusterPermission->id === $permission->id ? 'bg-blue-800' : 'bg-slate-800' }}">
                            <div class="{{ $clusterPermission->id === $permission->id ? 'text-blue-200' : 'text-amber-200' }} text-xs">
                                Description:
                            </div>
                            <div class="text-sm {{ $clusterPermission->id === $permission->id ? 'text-blue-100' : 'text-slate-200' }} line-clamp-2">
                                {{ $clusterPermission->description ?: 'No description available' }}
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <div class="flex justify-end mt-4">
                <x-button class="bg-slate-600 hover:bg-slate-500" wire:click="$set('revealViewPermissionModal', false)">
                    {{ __('Close') }}
                </x-button>
            </div>
        </x-slot>
    </x-dialog-modal>
</div>
