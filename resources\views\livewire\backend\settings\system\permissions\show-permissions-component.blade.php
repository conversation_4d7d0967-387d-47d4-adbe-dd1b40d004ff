@php
    $tablePagination = $this->permissions->isNotEmpty() && $this->totalPermissions > 5 ? true : false;
    $permissionsAvailable = $tablePagination;
    $rowNumber = 0;
@endphp
<div>
    <livewire:backend.settings.system.permissions.create-permissions-component />

    {{-- Search --}}
    <x-search :results="$permissionsAvailable" searchProperty="permissionsSearch"
        searchPlaceholder="Search by permission names...." formWidth="max-w-full my-3" />

    {{-- Table --}}
    <x-table :columns="[
        [
            'label' => '#',
            'headerClass' => 'border-slate-600',
        ],
        [
            'label' => 'Permission Name',
            'columnName' => 'name',
            'headerClass' => 'border-l border-slate-600 w-48',
        ],
        [
            'label' => 'Slug',
            'columnName' => 'slug',
            'headerClass' => 'border-l border-slate-600 w-32',
        ],
        [
            'label' => 'Cluster',
            'columnName' => 'cluster',
            'headerClass' => 'border-l border-slate-600 w-24 text-center',
        ],
        [
            'label' => 'Description',
            'headerClass' => 'border-l border-slate-600 line-clamp-1',
        ],
        [
            'label' => 'Actions',
            'headerClass' => 'border-l border-slate-600 w-24',
        ],
    ]" :data="$this->permissions" captionClass="text-slate-200" :sort-direction="$sortDirection" :sort-column="$sortColumn"
        :results="$permissionsAvailable" maxScrollHeight="max-h-[500px] hidden lg:block pr-1"
        theadClass="bg-slate-700 border-l border-slate-700" subtheadClass="px-2"
        rowClass="border-b hover:bg-slate-700 hover:text-slate-100 hover:border-y-2 hover:border-slate-600"
        tableClass="border-2 border-slate-700">

        @foreach ($this->permissions as $permission)
            <tr wire:key="{{ 'permission-' . $permission->id }}"
                class="border-b bg-slate-800 hover:bg-slate-700 border-slate-700">
                <td class="px-2 py-1 text-slate-200">
                    {{ $loop->iteration }}
                </td>
                <td class="border-l border-slate-600 px-2 py-1 text-slate-200 w-48">
                    {{ $permission->name }}
                </td>
                <td class="border-l border-slate-600 px-2 py-1 text-slate-200 w-32">
                    <code class="text-xs bg-slate-900 px-1 py-0.5 rounded">{{ $permission->slug }}</code>
                </td>
                <td class="border-l border-slate-600 px-2 py-1 text-slate-200 w-24 text-center">
                    <span class="bg-blue-600 text-white px-2 py-0.5 rounded-full text-xs">{{ $permission->cluster }}</span>
                </td>
                <td class="border-l border-slate-600 px-2 py-1 text-slate-200 line-clamp-1">
                    {{ $permission->description }}
                </td>
                <td class="border-l border-slate-600 px-2 py-1 w-24">
                    <span class="flex justify-end items-center space-x-1">
                        {{-- Action buttons --}}
                        <livewire:backend.settings.system.permissions.view-permissions-component :$permission
                            :key="'view-permission-' . $permission->id">
                            <livewire:backend.settings.system.permissions.update-permissions-component :$permission
                                :key="'update-permission-' . $permission->id">
                                <livewire:backend.settings.system.permissions.delete-permissions-component :$permission
                                    :key="'delete-permission-' . $permission->id">
                    </span>
                </td>
            </tr>
        @endforeach
    </x-table>

    {{-- Table List (Mobile View) --}}
    <x-table-list :data="$this->permissions" captionMobileClass="text-slate-200"
        maxScrollHeight="max-h-[500px] lg:hidden pr-1">
        @foreach ($this->permissions as $permission)
            @php
                $rowNumber++;
            @endphp
            <div wire:key="{{ 'permission-mobile-' . $permission->id }}"
                class="p-2 duration-300 ease-in-out rounded-md shadow bg-slate-500 text-slate-50 hover:bg-slate-600 hover:shadow-lg">
                <div class="flex justify-between items-center mb-1 pr-1">
                    <span class="text-sm text-slate-100">Name:</span>
                    <span class="text-sm text-slate-100">{{ $permission->name }}</span>
                </div>
                <div class="flex justify-between items-center mb-1 pr-1">
                    <span class="text-sm text-slate-100">Slug:</span>
                    <code class="text-xs bg-slate-900 px-1 py-0.5 rounded text-slate-200">{{ $permission->slug }}</code>
                </div>
                <div class="flex justify-between items-center mb-1 pr-1">
                    <span class="text-sm text-slate-100">Cluster:</span>
                    <span class="bg-blue-600 text-white px-2 py-0.5 rounded-full text-xs">{{ $permission->cluster }}</span>
                </div>
                <div class="mb-1 block bg-slate-800 rounded px-1.5 py-1">
                    <div class="text-amber-200">Description:</div>
                    <div class="text-sm text-slate-200 line-clamp-2">{{ $permission->description }}</div>
                </div>
                <div class="flex justify-between items-center">
                    <span class="flex justify-end items-center space-x-1">
                        {{-- Action buttons --}}
                        <livewire:backend.settings.system.permissions.view-permissions-component :$permission
                            :key="'view-mobile-permission-' . $permission->id">
                            <livewire:backend.settings.system.permissions.update-permissions-component :$permission
                                :key="'update-mobile-permission-' . $permission->id">
                                <livewire:backend.settings.system.permissions.delete-permissions-component :$permission
                                    :key="'delete-mobile-permission-' . $permission->id">
                    </span>
                    @if ($rowNumber >= 1)
                        <span class="text-right rounded-full px-2 bg-slate-950 text-slate-50 text-xs">{{ $rowNumber }}</span>
                    @endif
                </div>
            </div>
        @endforeach
    </x-table-list>

    {{-- Pagination --}}
    {{-- <x-paginator :pages="$this->permissionsPages" :totalRecords="$this->totalPermissions" selectClass="text-slate-500" paginateRecords="permissionsPaginate"
        :results="$permissionsAvailable" :paginationEnabled="$tablePagination" /> --}}

</div>
