<?php

namespace App\Repositories\Permissions;

use Illuminate\Support\Facades\Cache;
use App\Contracts\Repos\PermissionsRepos\CountFirstPermissionsPerClusterRepositoryInterface;

final class CachedCountFirstPermissionsPerClusterRepository implements CountFirstPermissionsPerClusterRepositoryInterface
{
    public function __construct(private CountFirstPermissionsPerClusterRepositoryInterface $countFirstPermissionsPerClusterRepository) {}

    public function handle(): int
    {
        $key   = 'permissions:count_first_per_cluster';
        $fresh = 300; // 5 minutes
        $stale = 900; // 15 minutes

        return Cache::flexible($key, [$fresh, $stale], function () {
            return $this->countFirstPermissionsPerClusterRepository->handle();
        });
    }
}
