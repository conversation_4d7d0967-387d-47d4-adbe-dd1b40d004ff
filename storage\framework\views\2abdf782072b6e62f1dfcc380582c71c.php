<?php
    $tablePagination = $this->permissions->isNotEmpty() && $this->totalPermissions > 5 ? true : false;
    $permissionsAvailable = $tablePagination;
    $rowNumber = 0;
?>
<div>
    <?php if (isset($component)) { $__componentOriginal9b33c063a2222f59546ad2a2a9a94bc6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9b33c063a2222f59546ad2a2a9a94bc6 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.search','data' => ['results' => $permissionsAvailable,'searchProperty' => 'permissionsSearch','searchPlaceholder' => 'Search by permission names....','formWidth' => 'max-w-full']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('search'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['results' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($permissionsAvailable),'searchProperty' => 'permissionsSearch','searchPlaceholder' => 'Search by permission names....','formWidth' => 'max-w-full']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9b33c063a2222f59546ad2a2a9a94bc6)): ?>
<?php $attributes = $__attributesOriginal9b33c063a2222f59546ad2a2a9a94bc6; ?>
<?php unset($__attributesOriginal9b33c063a2222f59546ad2a2a9a94bc6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9b33c063a2222f59546ad2a2a9a94bc6)): ?>
<?php $component = $__componentOriginal9b33c063a2222f59546ad2a2a9a94bc6; ?>
<?php unset($__componentOriginal9b33c063a2222f59546ad2a2a9a94bc6); ?>
<?php endif; ?>


    <div class="max-h-[400px] overflow-y-auto custom-scrollbar pe-3 ps-0 pt-2 shadow-sm">
        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $this->permissions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $permission): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div wire:key="<?php echo e('permission-' . $permission->id); ?>"
                class="duration-300 ease-in-out rounded-none sm:pb-0  sm:bg-transparent">
                <div class="bg-slate-800 mb-0 rounded">
                    <dt class="flex  items-center mt-1 md:mx-1 mb-0 text-sm text-slate-300 md:text-base">
                        <!-- Buttons -->
                        <div class=" flex items-center md:space-x-1 ">

                            <span class="flex justify-end items-center space-x-1">
                                
                                <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('backend.settings.system.permissions.view-permissions-component', ['permission' => $permission]);

$__html = app('livewire')->mount($__name, $__params, 'view-permission-' . $permission->id, $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                                    <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('backend.settings.system.permissions.update-permissions-component', ['permission' => $permission]);

$__html = app('livewire')->mount($__name, $__params, 'update-permission-' . $permission->id, $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                                        <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('backend.settings.system.permissions.delete-permissions-component', ['permission' => $permission]);

$__html = app('livewire')->mount($__name, $__params, 'delete-permission-' . $permission->id, $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                            </span>


                            <!-- Permission Name -->
                            <div class="line-clamp-1 ms-1">
                                <?php echo e($permission->name . ' Authorisation'); ?>

                                <?php echo e($permission->slug); ?>


                            </div>
                        </div>
                    </dt>
                </div>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
    </div>

    
    


</div>
<?php /**PATH C:\Users\<USER>\Herd\laraauthstarter\resources\views/livewire/backend/settings/system/permissions/show-permissions-component.blade.php ENDPATH**/ ?>