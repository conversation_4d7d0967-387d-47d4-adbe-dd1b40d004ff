<?php

namespace App\Models;

use App\Builders\PermissionRoleBuilder;
use App\Models\Role;
use App\Models\Permission;
use App\Traits\Defaults\HasFilterables;
use App\Traits\Defaults\HasSearchables;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class PermissionRole extends Model
{
    /** @use HasFactory<\Database\Factories\PermissionRoleFactory> */
    use HasFactory;
    use HasSearchables;
    use HasFilterables;

    protected $fillable = [
        'permission_id',
        'role_id',
    ];

    protected $casts = [
        'permission_id' => 'integer',
        'role_id' => 'integer',
    ];

    public function newEloquentBuilder($query)
    {
        return new PermissionRoleBuilder($query);
    }


}
