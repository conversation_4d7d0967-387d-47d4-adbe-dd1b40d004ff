<?php

namespace App\Repositories\Permissions;

use Illuminate\Support\Facades\Cache;
use App\Contracts\Repos\PermissionsRepos\ForgetCachedCountFirstPermissionsPerClusterRepositoryInterface;

final class ForgetCachedCountFirstPermissionsPerClusterRepository implements ForgetCachedCountFirstPermissionsPerClusterRepositoryInterface
{
    public function handle(): void
    {
        $key = 'permissions:count_first_per_cluster';
        Cache::forget($key);
    }
}
