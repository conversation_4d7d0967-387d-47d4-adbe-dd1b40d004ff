<?php

namespace App\Livewire\Backend\Settings\System\Permissions;

use Livewire\Component;
use Livewire\Attributes\Locked;
use Livewire\Attributes\Computed;

class UpdatePermissionsComponent extends Component
{
    #[Locked]
    public $permission;


    public function updatePermission()
    {
        // TODO: Implement permission update logic
    }

    public function render()
    {
        return view('livewire.backend.settings.system.permissions.update-permissions-component');
    }

    public function placeholder()
    {
        return view('placeholders.lazy-spin-loader');
    }
}
