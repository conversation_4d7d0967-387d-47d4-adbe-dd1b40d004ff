<?php

namespace App\Builders;

use Illuminate\Database\Eloquent\Builder;

class PermissionRoleBuilder extends Builder
{
    public function orderByIdAsc(): self
    {
        return $this->orderBy('id', 'asc');
    }

    public function orderByIdDesc(): self
    {
        return $this->orderBy('id', 'desc');
    }

    public function orderByPermissionIdAsc(): self
    {
        return $this->orderBy('permission_id', 'asc');
    }

    public function orderByPermissionIdDesc(): self
    {
        return $this->orderBy('permission_id', 'desc');
    }

    public function orderByRoleIdAsc(): self
    {
        return $this->orderBy('role_id', 'asc');
    }

    public function orderByRoleIdDesc(): self
    {
        return $this->orderBy('role_id', 'desc');
    }

    public function wherePermissionId(int $permissionId): self
    {
        return $this->where('permission_id', $permissionId);
    }

    public function whereRoleId(int $roleId): self
    {
        return $this->where('role_id', $roleId);
    }

    public function wherePermissionAndRole(int $permissionId, int $roleId): self
    {
        return $this->where('permission_id', $permissionId)
                    ->where('role_id', $roleId);
    }
}
