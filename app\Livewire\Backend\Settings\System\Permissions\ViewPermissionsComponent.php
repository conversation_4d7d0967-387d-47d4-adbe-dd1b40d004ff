<?php

namespace App\Livewire\Backend\Settings\System\Permissions;

use App\Models\Permission;
use Livewire\Component;
use Livewire\Attributes\Locked;
use Livewire\Attributes\Computed;

class ViewPermissionsComponent extends Component
{
    #[Locked]
    public $permission;

    public $revealViewPermissionModal = false;

    #[Computed]
    public function clusterPermissions()
    {
        return Permission::allPermissionsInCluster($this->permission->cluster)->get();
    }

    #[Computed]
    public function clusterInfo()
    {
        return [
            'cluster_id' => $this->permission->cluster,
            'total_permissions' => $this->clusterPermissions->count(),
            'cluster_name' => 'Cluster ' . $this->permission->cluster
        ];
    }

    public function openModalToViewPermission()
    {
        $this->revealViewPermissionModal = true;
    }

    public function render()
    {
        return view('livewire.backend.settings.system.permissions.view-permissions-component');
    }

    public function placeholder()
    {
        return view('placeholders.lazy-spin-loader');
    }
}
