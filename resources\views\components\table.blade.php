@props([
    'columns' => [],
    'data' => null,
    'results' => false,
    'sortDirection' => 'DESC',
    'sortColumn' => 'id',
    'captionClass' => 'hidden lg:block',
    'maxScrollHeight' => 'max-h-[350px] hidden lg:block',
    'tableContainerClass' => '',
    'tableClass' => '',
    'theadClass' => '',
    'subtheadClass' => '',
    'rowClass' => '',
])

<h1 class="mb-2  text-lg font-bold text-left rtl:text-right  {{ $captionClass }}">
    {{ $caption ?? '' }}
</h1>
<div class="overflow-y-auto custom-scrollbar shadow-sm {{ $maxScrollHeight }}">
    <div class="relative overflow-x-auto shadow-md rounded-lg {{ $tableContainerClass }}">
        <table class="w-full text-sm rtl:text-right text-slate-400 {{ $tableClass }}">
            <thead class="text-xs uppercase {{ $theadClass }}">
                <tr>
                    @foreach ($columns as $column)
                        @php
                            $colName = $column['columnName'] ?? null;
                        @endphp

                        <th @if ($results && $colName) wire:click="doSort('{{ $colName }}')" role="button" @endif
                            scope="col"
                            class="cursor-pointer py-2 {{ $subtheadClass ?? '' }} {{ $column['headerClass'] ?? '' }}">
                            <div>

                                <div class="flex items-center space-x-1.5">
                                    <span>{{ $column['label'] }}</span>
                                    @if ($results && $colName)
                                        <x-sort-columns :columnName="$colName" :sort-direction="$sortDirection" :sort-column="$sortColumn" />
                                    @endif
                                </div>
                            </div>

                        </th>
                    @endforeach
                </tr>
            </thead>

            <tbody>
                @if ($data->isNotEmpty())
                    {{ $slot }}
                @else
                    <tr class=" bg-slate-800  border-slate-700  {{ $rowClass }}">
                        <td colspan="{{ count($columns) }}" class="px-6 py-4 text-center text-slate-400 ">
                            <x-notice class="grid grid-cols-1 bg-amber-100 text-amber-700 py-1 border mt-1"
                                noticeClass="uppercase text-xs">
                                <x-slot:notice>{{ 'Oops!' }}</x-slot>
                                {{ __('No records found in the database') }}
                            </x-notice>
                        </td>
                    </tr>
                @endif
            </tbody>
        </table>
    </div>
</div>
