<div>
    <?php if (isset($component)) { $__componentOriginalf9332b595ad3d3a806f9da4dda8769dd = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf9332b595ad3d3a806f9da4dda8769dd = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.action-buttons','data' => ['id' => $permission->id,'viewAction' => 'openModalToViewPermission']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('action-buttons'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['id' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($permission->id),'viewAction' => 'openModalToViewPermission']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf9332b595ad3d3a806f9da4dda8769dd)): ?>
<?php $attributes = $__attributesOriginalf9332b595ad3d3a806f9da4dda8769dd; ?>
<?php unset($__attributesOriginalf9332b595ad3d3a806f9da4dda8769dd); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf9332b595ad3d3a806f9da4dda8769dd)): ?>
<?php $component = $__componentOriginalf9332b595ad3d3a806f9da4dda8769dd; ?>
<?php unset($__componentOriginalf9332b595ad3d3a806f9da4dda8769dd); ?>
<?php endif; ?>

    <?php if (isset($component)) { $__componentOriginal49bd1c1dd878e22e0fb84faabf295a3f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal49bd1c1dd878e22e0fb84faabf295a3f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dialog-modal','data' => ['wire:model' => 'revealViewPermissionModal','maxWidth' => '2xl']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dialog-modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['wire:model' => 'revealViewPermissionModal','maxWidth' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute('2xl')]); ?>
         <?php $__env->slot('content', null, []); ?> 
            <?php if (isset($component)) { $__componentOriginal73a6b0261bc3d280b80775663eef6108 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal73a6b0261bc3d280b80775663eef6108 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.divider','data' => ['nameClass' => 'text-blue-400']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('divider'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['nameClass' => 'text-blue-400']); ?>
                <?php echo e(__('CLUSTER PERMISSIONS')); ?>

             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal73a6b0261bc3d280b80775663eef6108)): ?>
<?php $attributes = $__attributesOriginal73a6b0261bc3d280b80775663eef6108; ?>
<?php unset($__attributesOriginal73a6b0261bc3d280b80775663eef6108); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal73a6b0261bc3d280b80775663eef6108)): ?>
<?php $component = $__componentOriginal73a6b0261bc3d280b80775663eef6108; ?>
<?php unset($__componentOriginal73a6b0261bc3d280b80775663eef6108); ?>
<?php endif; ?>

            <div class="text-slate-200 text-base font-semibold my-2">
                <?php echo e($this->clusterInfo['cluster_name']); ?> (<?php echo e($this->clusterInfo['total_permissions']); ?> permissions)
            </div>

            <div class="text-slate-200 text-sm my-2">
                Showing all permissions in cluster <?php echo e($this->clusterInfo['cluster_id']); ?>:
            </div>

            
            <div class="max-h-[400px] overflow-y-auto custom-scrollbar space-y-2">
                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $this->clusterPermissions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $clusterPermission): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="p-3 duration-300 ease-in-out rounded-md shadow
                        <?php echo e($clusterPermission->id === $permission->id
                            ? 'bg-blue-600 text-blue-50 hover:bg-blue-700 border-2 border-blue-400'
                            : 'bg-slate-500 text-slate-50 hover:bg-slate-600 hover:shadow-lg'); ?>">

                        
                        <div class="flex justify-between items-center mb-2">
                            <div class="flex items-center space-x-2">
                                <span class="text-sm font-semibold"><?php echo e($clusterPermission->name); ?></span>
                                <!--[if BLOCK]><![endif]--><?php if($clusterPermission->id === $permission->id): ?>
                                    <span class="text-xs bg-blue-800 text-blue-100 px-2 py-0.5 rounded-full font-medium">
                                        Current
                                    </span>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            </div>
                            <span class="text-right rounded-full px-2 py-0.5
                                <?php echo e($clusterPermission->id === $permission->id ? 'bg-blue-800 text-blue-100' : 'bg-slate-950 text-slate-50'); ?>

                                text-xs">
                                #<?php echo e($loop->iteration); ?>

                            </span>
                        </div>

                        
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-sm <?php echo e($clusterPermission->id === $permission->id ? 'text-blue-100' : 'text-slate-100'); ?>">
                                Slug:
                            </span>
                            <code class="text-xs px-2 py-1 rounded
                                <?php echo e($clusterPermission->id === $permission->id ? 'bg-blue-800 text-blue-100' : 'bg-slate-900 text-slate-200'); ?>">
                                <?php echo e($clusterPermission->slug); ?>

                            </code>
                        </div>

                        
                        <div class="block rounded px-2 py-2
                            <?php echo e($clusterPermission->id === $permission->id ? 'bg-blue-800' : 'bg-slate-800'); ?>">
                            <div class="<?php echo e($clusterPermission->id === $permission->id ? 'text-blue-200' : 'text-amber-200'); ?>">
                                Description:
                            </div>
                            <div class="text-sm <?php echo e($clusterPermission->id === $permission->id ? 'text-blue-100' : 'text-slate-200'); ?> line-clamp-2">
                                <?php echo e($clusterPermission->description ?: 'No description available'); ?>

                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
            </div>

            <div class="flex justify-end mt-4">
                <?php if (isset($component)) { $__componentOriginald0f1fd2689e4bb7060122a5b91fe8561 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.button','data' => ['class' => 'bg-slate-600 hover:bg-slate-500','wire:click' => '$set(\'revealViewPermissionModal\', false)']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'bg-slate-600 hover:bg-slate-500','wire:click' => '$set(\'revealViewPermissionModal\', false)']); ?>
                    <?php echo e(__('Close')); ?>

                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561)): ?>
<?php $attributes = $__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561; ?>
<?php unset($__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald0f1fd2689e4bb7060122a5b91fe8561)): ?>
<?php $component = $__componentOriginald0f1fd2689e4bb7060122a5b91fe8561; ?>
<?php unset($__componentOriginald0f1fd2689e4bb7060122a5b91fe8561); ?>
<?php endif; ?>
            </div>
         <?php $__env->endSlot(); ?>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal49bd1c1dd878e22e0fb84faabf295a3f)): ?>
<?php $attributes = $__attributesOriginal49bd1c1dd878e22e0fb84faabf295a3f; ?>
<?php unset($__attributesOriginal49bd1c1dd878e22e0fb84faabf295a3f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal49bd1c1dd878e22e0fb84faabf295a3f)): ?>
<?php $component = $__componentOriginal49bd1c1dd878e22e0fb84faabf295a3f; ?>
<?php unset($__componentOriginal49bd1c1dd878e22e0fb84faabf295a3f); ?>
<?php endif; ?>
</div>
<?php /**PATH C:\Users\<USER>\Herd\laraauthstarter\resources\views/livewire/backend/settings/system/permissions/view-permissions-component.blade.php ENDPATH**/ ?>