<div>
    <x-button class="bg-slate-600 hover:bg-green-600" wire:click="openModalToCreateRole" target="openModalToCreateRole"
        icon="circle-plus">
        {{ __('Create Role') }}
    </x-button>

    <x-dialog-modal wire:model="revealCreateRolesModal" :maxWidth="'xl'">
        <x-slot name="content">
            <x-divider nameClass="text-green-400">
                {{ __('CREATE A ROLE') }}
            </x-divider>

            <x-road-access />
            <x-rate-limiter-message :message="$this->roadAccessForm->road_access_rate_limiter" />

            @if (!$this->roadAccessForm->road_access_rate_limiter)
                <x-lable-input-error id="form.name" type="text" label="Role name" model="form.name" inputType="input"
                    value="{{ $form->name }}" :onEnter="$form->getCreateRoleEvent()" placeholder="Enter role name...."
                    modelModifier="live.debounce.400ms" inputClass="w-full" icon="user-tag" iconClass="ps-2.5" />

                <x-lable-input-error id="form.description" type="text" label="Role description"
                    model="form.description" inputType="textarea" value="{{ $form->description }}" :onEnter="$form->getCreateRoleEvent()"
                    placeholder="Enter role description...." modelModifier="live.debounce.500ms" inputClass="w-full"
                    parentClass="mt-3" />

                <x-lable-select-error parentClass="mt-2" labelClass="w-full text-slate-300"
                    selectClass="font-bold text-green-800" id="form.type" label="Role type" model="form.type"
                    placeholder="Select role type" :options="$this->roleTypeOptions" :value="$form->type"
                    modelModifier="live.debounce.500ms" />
            @endif

            <div class="sm:flex items-center mt-4">
                <x-button class="bg-red-600 hover:bg-red-500" wire:click="$toggle('revealCreateRolesModal')"
                    target="revealCreateRolesModal" icon='times'>
                    {{ __('Cancel') }}
                </x-button>

                @if (!$this->roadAccessForm->road_access_rate_limiter)
                    <x-button class="ms-3 bg-green-600 hover:bg-green-500" wire:click="createRole" target="createRole"
                        :disabled="$form->hasErrors()" icon='check'>
                        {{ __('Create') }}
                    </x-button>
                @endif

                <x-action-message class="sm:ms-3 mt-2 sm:mt-0" on="roleCreated">
                    {{ __('Role created successfully.') }}
                </x-action-message>

                <x-action-message class="sm:ms-3 mt-2 sm:mt-0 bg-red-600 text-red-50" on="roleCreationFailed">
                    {{ __('Error occured while trying to create role') }}
                </x-action-message>
            </div>
        </x-slot>
    </x-dialog-modal>


</div>
