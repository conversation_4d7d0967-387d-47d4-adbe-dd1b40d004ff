<div>
    <x-button class="bg-slate-600 hover:bg-green-600" wire:click="openModalToCreateRole" target="openModalToCreateRole"
        icon="circle-plus">
        {{ __('Create Role') }}
    </x-button>

    <x-dialog-modal wire:model="revealCreateRolesModal" :maxWidth="'2xl'">
        <x-slot name="content">
            <x-divider nameClass="text-green-400">
                {{ __('CREATE NEW ROLE') }}
            </x-divider>

            {{-- Header Section --}}
            <div class="bg-gradient-to-r from-green-600 to-green-700 rounded-lg p-4 mb-6 border border-green-500">
                <div class="flex items-center space-x-3">
                    <div class="bg-green-800 p-2 rounded-full">
                        <i class="fa-solid fa-user-plus text-green-100 text-lg"></i>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold text-white">Add New Role</h3>
                        <p class="text-green-100 text-sm">Create a new role with specific permissions and access levels</p>
                    </div>
                </div>
            </div>

            <x-road-access />
            <x-rate-limiter-message :message="$this->roadAccessForm->road_access_rate_limiter" />

            @if (!$this->roadAccessForm->road_access_rate_limiter)
                {{-- Form Fields Container --}}
                <div class="space-y-4">
                    {{-- Role Name Field --}}
                    <div class="bg-slate-700 rounded-lg p-4 border border-slate-600">
                        <x-lable-input-error id="form.name" type="text" label="Role Name" model="form.name" inputType="input"
                            value="{{ $form->name }}" :onEnter="$form->getCreateRoleEvent()" placeholder="Enter role name (e.g., Administrator, Editor)..."
                            modelModifier="live.debounce.400ms" inputClass="w-full bg-slate-800 border-slate-600 text-slate-200 focus:border-green-500 focus:ring-green-500"
                            icon="user-tag" iconClass="ps-2.5" />
                    </div>

                    {{-- Role Type Field --}}
                    <div class="bg-slate-700 rounded-lg p-4 border border-slate-600">
                        <x-lable-select-error parentClass="" labelClass="w-full text-slate-300 font-medium"
                            selectClass="font-bold text-green-800 bg-slate-800 border-slate-600 focus:border-green-500 focus:ring-green-500"
                            id="form.type" label="Role Type" model="form.type"
                            placeholder="Select role type" :options="$this->roleTypeOptions" :value="$form->type"
                            modelModifier="live.debounce.500ms" />
                    </div>

                    {{-- Role Description Field --}}
                    <div class="bg-slate-700 rounded-lg p-4 border border-slate-600">
                        <x-lable-input-error id="form.description" type="text" label="Role Description"
                            model="form.description" inputType="textarea" value="{{ $form->description }}" :onEnter="$form->getCreateRoleEvent()"
                            placeholder="Describe the role's responsibilities and access level..."
                            modelModifier="live.debounce.500ms"
                            inputClass="w-full bg-slate-800 border-slate-600 text-slate-200 focus:border-green-500 focus:ring-green-500 min-h-[100px]" />
                    </div>
                </div>
            @endif

            {{-- Action Buttons --}}
            <div class="flex flex-col sm:flex-row items-center justify-between mt-6 pt-4 border-t border-slate-600">
                <div class="flex space-x-3">
                    <x-button class="bg-red-600 hover:bg-red-500" wire:click="$toggle('revealCreateRolesModal')"
                        target="revealCreateRolesModal" icon='times'>
                        {{ __('Cancel') }}
                    </x-button>

                    @if (!$this->roadAccessForm->road_access_rate_limiter)
                        <x-button class="bg-green-600 hover:bg-green-500" wire:click="createRole" target="createRole"
                            :disabled="$form->hasErrors()" icon='check'>
                            {{ __('Create Role') }}
                        </x-button>
                    @endif
                </div>

                {{-- Success/Error Messages --}}
                <div class="mt-3 sm:mt-0">
                    <x-action-message class="bg-green-600 text-green-50 px-3 py-1 rounded" on="roleCreated">
                        {{ __('Role created successfully!') }}
                    </x-action-message>

                    <x-action-message class="bg-red-600 text-red-50 px-3 py-1 rounded" on="roleCreationFailed">
                        {{ __('Error occurred while creating role') }}
                    </x-action-message>
                </div>
            </div>
        </x-slot>
    </x-dialog-modal>


</div>
