<div>
    <?php if (isset($component)) { $__componentOriginald0f1fd2689e4bb7060122a5b91fe8561 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.button','data' => ['class' => 'bg-slate-600 hover:bg-green-600','wire:click' => 'openModalToCreateRole','target' => 'openModalToCreateRole','icon' => 'circle-plus']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'bg-slate-600 hover:bg-green-600','wire:click' => 'openModalToCreateRole','target' => 'openModalToCreateRole','icon' => 'circle-plus']); ?>
        <?php echo e(__('Create Role')); ?>

     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561)): ?>
<?php $attributes = $__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561; ?>
<?php unset($__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald0f1fd2689e4bb7060122a5b91fe8561)): ?>
<?php $component = $__componentOriginald0f1fd2689e4bb7060122a5b91fe8561; ?>
<?php unset($__componentOriginald0f1fd2689e4bb7060122a5b91fe8561); ?>
<?php endif; ?>

    <?php if (isset($component)) { $__componentOriginal49bd1c1dd878e22e0fb84faabf295a3f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal49bd1c1dd878e22e0fb84faabf295a3f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dialog-modal','data' => ['wire:model' => 'revealCreateRolesModal','maxWidth' => '2xl']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dialog-modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['wire:model' => 'revealCreateRolesModal','maxWidth' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute('2xl')]); ?>
         <?php $__env->slot('content', null, []); ?> 
            <?php if (isset($component)) { $__componentOriginal73a6b0261bc3d280b80775663eef6108 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal73a6b0261bc3d280b80775663eef6108 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.divider','data' => ['nameClass' => 'text-green-400']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('divider'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['nameClass' => 'text-green-400']); ?>
                <?php echo e(__('CREATE NEW ROLE')); ?>

             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal73a6b0261bc3d280b80775663eef6108)): ?>
<?php $attributes = $__attributesOriginal73a6b0261bc3d280b80775663eef6108; ?>
<?php unset($__attributesOriginal73a6b0261bc3d280b80775663eef6108); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal73a6b0261bc3d280b80775663eef6108)): ?>
<?php $component = $__componentOriginal73a6b0261bc3d280b80775663eef6108; ?>
<?php unset($__componentOriginal73a6b0261bc3d280b80775663eef6108); ?>
<?php endif; ?>

            
            <div class="bg-gradient-to-r from-green-600 to-green-700 rounded-lg p-4 mb-6 border border-green-500">
                <div class="flex items-center space-x-3">
                    <div class="bg-green-800 p-2 rounded-full">
                        <i class="fa-solid fa-user-plus text-green-100 text-lg"></i>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold text-white">Add New Role</h3>
                        <p class="text-green-100 text-sm">Create a new role with specific permissions and access levels</p>
                    </div>
                </div>
            </div>

            <?php if (isset($component)) { $__componentOriginal7f532b4338f78b38e13c5b83e10dff77 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7f532b4338f78b38e13c5b83e10dff77 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.road-access','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('road-access'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7f532b4338f78b38e13c5b83e10dff77)): ?>
<?php $attributes = $__attributesOriginal7f532b4338f78b38e13c5b83e10dff77; ?>
<?php unset($__attributesOriginal7f532b4338f78b38e13c5b83e10dff77); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7f532b4338f78b38e13c5b83e10dff77)): ?>
<?php $component = $__componentOriginal7f532b4338f78b38e13c5b83e10dff77; ?>
<?php unset($__componentOriginal7f532b4338f78b38e13c5b83e10dff77); ?>
<?php endif; ?>
            <?php if (isset($component)) { $__componentOriginal834204310c6424a466154465717204ee = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal834204310c6424a466154465717204ee = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.rate-limiter-message','data' => ['message' => $this->roadAccessForm->road_access_rate_limiter]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('rate-limiter-message'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['message' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($this->roadAccessForm->road_access_rate_limiter)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal834204310c6424a466154465717204ee)): ?>
<?php $attributes = $__attributesOriginal834204310c6424a466154465717204ee; ?>
<?php unset($__attributesOriginal834204310c6424a466154465717204ee); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal834204310c6424a466154465717204ee)): ?>
<?php $component = $__componentOriginal834204310c6424a466154465717204ee; ?>
<?php unset($__componentOriginal834204310c6424a466154465717204ee); ?>
<?php endif; ?>

            <!--[if BLOCK]><![endif]--><?php if(!$this->roadAccessForm->road_access_rate_limiter): ?>
                
                <div class="space-y-4">
                    
                    <div class="bg-slate-700 rounded-lg p-4 border border-slate-600">
                        <?php if (isset($component)) { $__componentOriginalc1874febf49a28dae7622f110fa0eeaf = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc1874febf49a28dae7622f110fa0eeaf = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.lable-input-error','data' => ['id' => 'form.name','type' => 'text','label' => 'Role Name','model' => 'form.name','inputType' => 'input','value' => ''.e($form->name).'','onEnter' => $form->getCreateRoleEvent(),'placeholder' => 'Enter role name (e.g., Administrator, Editor)...','modelModifier' => 'live.debounce.400ms','inputClass' => 'w-full bg-slate-800 border-slate-600 text-slate-200 focus:border-green-500 focus:ring-green-500','icon' => 'user-tag','iconClass' => 'ps-2.5']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('lable-input-error'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['id' => 'form.name','type' => 'text','label' => 'Role Name','model' => 'form.name','inputType' => 'input','value' => ''.e($form->name).'','onEnter' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($form->getCreateRoleEvent()),'placeholder' => 'Enter role name (e.g., Administrator, Editor)...','modelModifier' => 'live.debounce.400ms','inputClass' => 'w-full bg-slate-800 border-slate-600 text-slate-200 focus:border-green-500 focus:ring-green-500','icon' => 'user-tag','iconClass' => 'ps-2.5']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc1874febf49a28dae7622f110fa0eeaf)): ?>
<?php $attributes = $__attributesOriginalc1874febf49a28dae7622f110fa0eeaf; ?>
<?php unset($__attributesOriginalc1874febf49a28dae7622f110fa0eeaf); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc1874febf49a28dae7622f110fa0eeaf)): ?>
<?php $component = $__componentOriginalc1874febf49a28dae7622f110fa0eeaf; ?>
<?php unset($__componentOriginalc1874febf49a28dae7622f110fa0eeaf); ?>
<?php endif; ?>
                    </div>

                    
                    <div class="bg-slate-700 rounded-lg p-4 border border-slate-600">
                        <?php if (isset($component)) { $__componentOriginal655b2d8713356738418255a5c8b1912b = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal655b2d8713356738418255a5c8b1912b = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.lable-select-error','data' => ['parentClass' => '','labelClass' => 'w-full text-slate-300 font-medium','selectClass' => 'font-bold text-green-800 bg-slate-800 border-slate-600 focus:border-green-500 focus:ring-green-500','id' => 'form.type','label' => 'Role Type','model' => 'form.type','placeholder' => 'Select role type','options' => $this->roleTypeOptions,'value' => $form->type,'modelModifier' => 'live.debounce.500ms']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('lable-select-error'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['parentClass' => '','labelClass' => 'w-full text-slate-300 font-medium','selectClass' => 'font-bold text-green-800 bg-slate-800 border-slate-600 focus:border-green-500 focus:ring-green-500','id' => 'form.type','label' => 'Role Type','model' => 'form.type','placeholder' => 'Select role type','options' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($this->roleTypeOptions),'value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($form->type),'modelModifier' => 'live.debounce.500ms']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal655b2d8713356738418255a5c8b1912b)): ?>
<?php $attributes = $__attributesOriginal655b2d8713356738418255a5c8b1912b; ?>
<?php unset($__attributesOriginal655b2d8713356738418255a5c8b1912b); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal655b2d8713356738418255a5c8b1912b)): ?>
<?php $component = $__componentOriginal655b2d8713356738418255a5c8b1912b; ?>
<?php unset($__componentOriginal655b2d8713356738418255a5c8b1912b); ?>
<?php endif; ?>
                    </div>

                    
                    <div class="bg-slate-700 rounded-lg p-4 border border-slate-600">
                        <?php if (isset($component)) { $__componentOriginalc1874febf49a28dae7622f110fa0eeaf = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc1874febf49a28dae7622f110fa0eeaf = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.lable-input-error','data' => ['id' => 'form.description','type' => 'text','label' => 'Role Description','model' => 'form.description','inputType' => 'textarea','value' => ''.e($form->description).'','onEnter' => $form->getCreateRoleEvent(),'placeholder' => 'Describe the role\'s responsibilities and access level...','modelModifier' => 'live.debounce.500ms','inputClass' => 'w-full bg-slate-800 border-slate-600 text-slate-200 focus:border-green-500 focus:ring-green-500 min-h-[100px]']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('lable-input-error'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['id' => 'form.description','type' => 'text','label' => 'Role Description','model' => 'form.description','inputType' => 'textarea','value' => ''.e($form->description).'','onEnter' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($form->getCreateRoleEvent()),'placeholder' => 'Describe the role\'s responsibilities and access level...','modelModifier' => 'live.debounce.500ms','inputClass' => 'w-full bg-slate-800 border-slate-600 text-slate-200 focus:border-green-500 focus:ring-green-500 min-h-[100px]']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc1874febf49a28dae7622f110fa0eeaf)): ?>
<?php $attributes = $__attributesOriginalc1874febf49a28dae7622f110fa0eeaf; ?>
<?php unset($__attributesOriginalc1874febf49a28dae7622f110fa0eeaf); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc1874febf49a28dae7622f110fa0eeaf)): ?>
<?php $component = $__componentOriginalc1874febf49a28dae7622f110fa0eeaf; ?>
<?php unset($__componentOriginalc1874febf49a28dae7622f110fa0eeaf); ?>
<?php endif; ?>
                    </div>
                </div>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

            
            <div class="flex flex-col sm:flex-row items-center justify-between mt-6 pt-4 border-t border-slate-600">
                <div class="flex space-x-3">
                    <?php if (isset($component)) { $__componentOriginald0f1fd2689e4bb7060122a5b91fe8561 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.button','data' => ['class' => 'bg-red-600 hover:bg-red-500','wire:click' => '$toggle(\'revealCreateRolesModal\')','target' => 'revealCreateRolesModal','icon' => 'times']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'bg-red-600 hover:bg-red-500','wire:click' => '$toggle(\'revealCreateRolesModal\')','target' => 'revealCreateRolesModal','icon' => 'times']); ?>
                        <?php echo e(__('Cancel')); ?>

                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561)): ?>
<?php $attributes = $__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561; ?>
<?php unset($__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald0f1fd2689e4bb7060122a5b91fe8561)): ?>
<?php $component = $__componentOriginald0f1fd2689e4bb7060122a5b91fe8561; ?>
<?php unset($__componentOriginald0f1fd2689e4bb7060122a5b91fe8561); ?>
<?php endif; ?>

                    <!--[if BLOCK]><![endif]--><?php if(!$this->roadAccessForm->road_access_rate_limiter): ?>
                        <?php if (isset($component)) { $__componentOriginald0f1fd2689e4bb7060122a5b91fe8561 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.button','data' => ['class' => 'bg-green-600 hover:bg-green-500','wire:click' => 'createRole','target' => 'createRole','disabled' => $form->hasErrors(),'icon' => 'check']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'bg-green-600 hover:bg-green-500','wire:click' => 'createRole','target' => 'createRole','disabled' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($form->hasErrors()),'icon' => 'check']); ?>
                            <?php echo e(__('Create Role')); ?>

                         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561)): ?>
<?php $attributes = $__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561; ?>
<?php unset($__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald0f1fd2689e4bb7060122a5b91fe8561)): ?>
<?php $component = $__componentOriginald0f1fd2689e4bb7060122a5b91fe8561; ?>
<?php unset($__componentOriginald0f1fd2689e4bb7060122a5b91fe8561); ?>
<?php endif; ?>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </div>

                
                <div class="mt-3 sm:mt-0">
                    <?php if (isset($component)) { $__componentOriginala665a74688c74e9ee80d4fedd2b98434 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginala665a74688c74e9ee80d4fedd2b98434 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.action-message','data' => ['class' => 'bg-green-600 text-green-50 px-3 py-1 rounded','on' => 'roleCreated']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('action-message'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'bg-green-600 text-green-50 px-3 py-1 rounded','on' => 'roleCreated']); ?>
                        <?php echo e(__('Role created successfully!')); ?>

                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginala665a74688c74e9ee80d4fedd2b98434)): ?>
<?php $attributes = $__attributesOriginala665a74688c74e9ee80d4fedd2b98434; ?>
<?php unset($__attributesOriginala665a74688c74e9ee80d4fedd2b98434); ?>
<?php endif; ?>
<?php if (isset($__componentOriginala665a74688c74e9ee80d4fedd2b98434)): ?>
<?php $component = $__componentOriginala665a74688c74e9ee80d4fedd2b98434; ?>
<?php unset($__componentOriginala665a74688c74e9ee80d4fedd2b98434); ?>
<?php endif; ?>

                    <?php if (isset($component)) { $__componentOriginala665a74688c74e9ee80d4fedd2b98434 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginala665a74688c74e9ee80d4fedd2b98434 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.action-message','data' => ['class' => 'bg-red-600 text-red-50 px-3 py-1 rounded','on' => 'roleCreationFailed']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('action-message'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'bg-red-600 text-red-50 px-3 py-1 rounded','on' => 'roleCreationFailed']); ?>
                        <?php echo e(__('Error occurred while creating role')); ?>

                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginala665a74688c74e9ee80d4fedd2b98434)): ?>
<?php $attributes = $__attributesOriginala665a74688c74e9ee80d4fedd2b98434; ?>
<?php unset($__attributesOriginala665a74688c74e9ee80d4fedd2b98434); ?>
<?php endif; ?>
<?php if (isset($__componentOriginala665a74688c74e9ee80d4fedd2b98434)): ?>
<?php $component = $__componentOriginala665a74688c74e9ee80d4fedd2b98434; ?>
<?php unset($__componentOriginala665a74688c74e9ee80d4fedd2b98434); ?>
<?php endif; ?>
                </div>
            </div>
         <?php $__env->endSlot(); ?>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal49bd1c1dd878e22e0fb84faabf295a3f)): ?>
<?php $attributes = $__attributesOriginal49bd1c1dd878e22e0fb84faabf295a3f; ?>
<?php unset($__attributesOriginal49bd1c1dd878e22e0fb84faabf295a3f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal49bd1c1dd878e22e0fb84faabf295a3f)): ?>
<?php $component = $__componentOriginal49bd1c1dd878e22e0fb84faabf295a3f; ?>
<?php unset($__componentOriginal49bd1c1dd878e22e0fb84faabf295a3f); ?>
<?php endif; ?>


</div>
<?php /**PATH C:\Users\<USER>\Herd\laraauthstarter\resources\views/livewire/backend/settings/system/roles/create-roles-component.blade.php ENDPATH**/ ?>