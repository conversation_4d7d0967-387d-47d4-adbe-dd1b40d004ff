<?php

namespace Database\Seeders;

use App\Models\Permission;
use Illuminate\Database\Seeder;

class PermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $permissions = [
            // User Management (cluster: 1)
            [
                'name' => 'Users',
                'slug' => 'users',
                'cluster' => 1,
                'description' => 'View user accounts and their details',
            ],
            [
                'name' => 'Create Users',
                'slug' => 'create-users',
                'cluster' => 1,
                'description' => 'Create new user accounts',
            ],
            [
                'name' => 'View Users',
                'slug' => 'view-users',
                'cluster' => 1,
                'description' => 'View user accounts and their details',
            ],
            [
                'name' => 'Update Users',
                'slug' => 'update-users',
                'cluster' => 1,
                'description' => 'Edit existing user accounts',
            ],
            [
                'name' => 'Delete Users',
                'slug' => 'delete-users',
                'cluster' => 1,
                'description' => 'Delete user accounts',
            ],

            // Role Management (cluster: 2)
            [
                'name' => 'Roles',
                'slug' => 'roles',
                'cluster' => 2,
                'description' => 'View roles and their details',
            ],
            [
                'name' => 'Create Roles',
                'slug' => 'create-roles',
                'cluster' => 2,
                'description' => 'Create new roles',
            ],
            [
                'name' => 'View Roles',
                'slug' => 'view-roles',
                'cluster' => 2,
                'description' => 'View roles and their details',
            ],
            [
                'name' => 'Update Roles',
                'slug' => 'update-roles',
                'cluster' => 2,
                'description' => 'Edit existing roles',
            ],
            [
                'name' => 'Delete Roles',
                'slug' => 'delete-roles',
                'cluster' => 2,
                'description' => 'Delete roles',
            ],

            // Permission Management (cluster: 3)
            [
                'name' => 'Permissions',
                'slug' => 'permissions',
                'cluster' => 3,
                'description' => 'View permissions and their details',
            ],
            [
                'name' => 'Create Permissions',
                'slug' => 'create-permissions',
                'cluster' => 3,
                'description' => 'Create new permissions',
            ],
            [
                'name' => 'View Permissions',
                'slug' => 'view-permissions',
                'cluster' => 3,
                'description' => 'View permissions and their details',
            ],
            [
                'name' => 'Update Permissions',
                'slug' => 'update-permissions',
                'cluster' => 3,
                'description' => 'Edit existing permissions',
            ],
            [
                'name' => 'Delete Permissions',
                'slug' => 'delete-permissions',
                'cluster' => 3,
                'description' => 'Delete permissions',
            ],

            // System Settings (cluster: 4)
            [
                'name' => 'Settings',
                'slug' => 'settings',
                'cluster' => 4,
                'description' => 'View and manage system configuration settings',
            ],
            [
                'name' => 'Create Settings',
                'slug' => 'create-settings',
                'cluster' => 4,
                'description' => 'Create new system configuration settings',
            ],
            [
                'name' => 'View Settings',
                'slug' => 'view-settings',
                'cluster' => 4,
                'description' => 'View system configuration settings',
            ],
            [
                'name' => 'Update Settings',
                'slug' => 'update-settings',
                'cluster' => 4,
                'description' => 'Modify system configuration settings',
            ],
            [
                'name' => 'Delete Settings',
                'slug' => 'delete-settings',
                'cluster' => 4,
                'description' => 'Delete system configuration settings',
            ],

            // Security (cluster: 5)
            [
                'name' => 'Security',
                'slug' => 'security',
                'cluster' => 5,
                'description' => 'View and manage security settings and configurations',
            ],
            [
                'name' => 'Create Security',
                'slug' => 'create-security',
                'cluster' => 5,
                'description' => 'Create new security settings and configurations',
            ],
            [
                'name' => 'View Security',
                'slug' => 'view-security',
                'cluster' => 5,
                'description' => 'View security settings and configurations',
            ],
            [
                'name' => 'Update Security',
                'slug' => 'update-security',
                'cluster' => 5,
                'description' => 'Manage allowed and blocked IP addresses',
            ],
            [
                'name' => 'Delete Security',
                'slug' => 'delete-security',
                'cluster' => 5,
                'description' => 'Delete security settings and configurations',
            ],
        ];

        foreach ($permissions as $permission) {
            Permission::updateOrCreate(
                ['name' => $permission['name']], // match by unique column
                [
                    'slug' => $permission['slug'],
                    'cluster' => $permission['cluster'],
                    'description' => $permission['description'],
                    'updated_at' => now(),
                ]
            );
        }
    }
}
