<?php

namespace Database\Seeders;

use App\Models\Permission;
use Illuminate\Database\Seeder;

class PermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $permissions = [
            // User Management (cluster: 1)
            [
                'name' => 'View Users',
                'slug' => 'users.view',
                'cluster' => 1,
                'description' => 'View user accounts and their details',
            ],
            [
                'name' => 'Create Users',
                'slug' => 'users.create',
                'cluster' => 1,
                'description' => 'Create new user accounts',
            ],
            [
                'name' => 'Edit Users',
                'slug' => 'users.edit',
                'cluster' => 1,
                'description' => 'Edit existing user accounts',
            ],
            [
                'name' => 'Delete Users',
                'slug' => 'users.delete',
                'cluster' => 1,
                'description' => 'Delete user accounts',
            ],

            // Role Management (cluster: 2)
            [
                'name' => 'View Roles',
                'slug' => 'roles.view',
                'cluster' => 2,
                'description' => 'View roles and their details',
            ],
            [
                'name' => 'Create Roles',
                'slug' => 'roles.create',
                'cluster' => 2,
                'description' => 'Create new roles',
            ],
            [
                'name' => 'Edit Roles',
                'slug' => 'roles.edit',
                'cluster' => 2,
                'description' => 'Edit existing roles',
            ],
            [
                'name' => 'Delete Roles',
                'slug' => 'roles.delete',
                'cluster' => 2,
                'description' => 'Delete roles',
            ],

            // Permission Management (cluster: 3)
            [
                'name' => 'View Permissions',
                'slug' => 'permissions.view',
                'cluster' => 3,
                'description' => 'View permissions and their details',
            ],
            [
                'name' => 'Manage Permissions',
                'slug' => 'permissions.manage',
                'cluster' => 3,
                'description' => 'Assign and revoke permissions',
            ],

            // System Settings (cluster: 4)
            [
                'name' => 'View System Settings',
                'slug' => 'settings.view',
                'cluster' => 4,
                'description' => 'View system configuration settings',
            ],
            [
                'name' => 'Manage System Settings',
                'slug' => 'settings.manage',
                'cluster' => 4,
                'description' => 'Modify system configuration settings',
            ],

            // Security (cluster: 5)
            [
                'name' => 'View Security Settings',
                'slug' => 'security.view',
                'cluster' => 5,
                'description' => 'View security settings and configurations',
            ],
            [
                'name' => 'Manage IP Restrictions',
                'slug' => 'security.ip-management',
                'cluster' => 5,
                'description' => 'Manage allowed and blocked IP addresses',
            ],
        ];

        foreach ($permissions as $permission) {
            Permission::create(array_merge($permission, [
                'created_at' => now(),
                'updated_at' => now(),
            ]));
        }
    }
}
