<?php

namespace App\Livewire\Backend\Settings\System\Permissions;

use Livewire\Component;
use App\Models\Permission;
use Livewire\Attributes\On;
use Livewire\Attributes\Url;
use Livewire\WithPagination;
use Livewire\Attributes\Computed;
use App\Supports\PaginatorSupport;
use App\Traits\Defaults\HasSortables;

class ShowPermissionsComponent extends Component
{
    use WithPagination;
    use HasSortables;

    #[Url]
    public $permissionsSearch = '';
    #[Url]
    public $permissionsPaginate = 5;

    public function doSort(string $column)
    {
        $this->sortingDirection($column);
    }

    #[Computed]
    public function permissions()
    {
        $selectedColumns = ['id', 'name', 'slug', 'cluster', 'description'];
        $searchTerm = trim('%' . $this->permissionsSearch . '%');

        $query = Permission::query();
        $query = $query->select($selectedColumns);
        $query = $query->search($searchTerm);
        $query = $query->firstPermissionPerCluster();
        $query = $this->sortBy($query, $this->sortColumn, $this->sortDirection);
        $query = $query->paginate($this->permissionsPaginate);

        return $query;

        // return collect();

    }

    #[Computed]
    public function totalPermissions()
    {
        // return CountPermissionsFacade::handle();
        return 0;
    }

    #[Computed()]
    public function permissionsPages(): array
    {
        return PaginatorSupport::generatePages($this->totalPermissions);
    }

    #[On(['permissionCreated', 'permissionUpdated', 'permissionDeleted'])]
    public function render()
    {
        return view('livewire.backend.settings.system.permissions.show-permissions-component');
    }

    public function placeholder()
    {
        return view('placeholders.lazy-spin-loader');
    }
}
