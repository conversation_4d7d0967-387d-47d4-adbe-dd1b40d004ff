<?php

namespace App\Livewire\Backend\Settings\System\Permissions;

use Livewire\Component;
use Livewire\Attributes\Locked;

class DeletePermissionsComponent extends Component
{
    #[Locked]
    public $permission;


    public function deletePermission()
    {
        // TODO: Implement permission deletion logic
    }

    public function render()
    {
        return view('livewire.backend.settings.system.permissions.delete-permissions-component');
    }

    public function placeholder()
    {
        return view('placeholders.lazy-spin-loader');
    }
}
