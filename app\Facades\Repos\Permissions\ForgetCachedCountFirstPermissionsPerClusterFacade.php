<?php

namespace App\Facades\Repos\Permissions;

use Illuminate\Support\Facades\Facade;
use App\Contracts\Repos\PermissionsRepos\ForgetCachedCountFirstPermissionsPerClusterRepositoryInterface;

class ForgetCachedCountFirstPermissionsPerClusterFacade extends Facade
{
    protected static function getFacadeAccessor(): string
    {
        return ForgetCachedCountFirstPermissionsPerClusterRepositoryInterface::class;
    }
}
